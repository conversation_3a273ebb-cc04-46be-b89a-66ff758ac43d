#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import json
from supabase import create_client, Client
import urllib3
import ssl
import time

PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/"

# Supabase 配置
SUPABASE_URL = "https://urjvwlqymdokurivceni.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVyanZ3bHF5bWRva3VyaXZjZW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTg5ODMsImV4cCI6MjA2NTk3NDk4M30.CFYHEExTSF4Q_X1xn3_BdiRH1p7YxbpeQitUL7O5Un4"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVyanZ3bHF5bWRva3VyaXZjZW5pIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDM5ODk4MywiZXhwIjoyMDY1OTc0OTgzfQ.rs-gne1v4__hymLb2Q9oyC4je0_AxJSnD81shQYCEzM"


def parse_pinpoint_page(html: str) -> dict:
    soup = BeautifulSoup(html, "html.parser")

    # 获取页面标题中的日期和编号
    h2 = soup.find("h2")
    date_str = None
    game_number = None

    # 从 H2 标签中提取日期
    if h2:
        h2_text = h2.get_text()
        # 匹配 "Today's Pinpoint Answer (2025-08-01)" 格式，兼容不同的单引号字符
        date_match = re.search(r"Today.s Pinpoint Answer\s*\((\d{4}-\d{1,2}-\d{1,2})\)", h2_text)
        if date_match:
            date_str = date_match.group(1)

    # 从整个页面文本中提取游戏编号
    content = soup.get_text()
    # 匹配 "The pinpoint game number is 458" 格式
    game_match = re.search(r"pinpoint game number is (\d+)", content, re.IGNORECASE)
    if game_match:
        game_number = int(game_match.group(1))

    # 文本提取整体块
    content = soup.get_text(separator="\n")
    # 提取答案类别： "Pinpoint Today Answer :" 后面行
    ans_match = re.search(r"Pinpoint Today Answer\s*:\s*\n?(.+)", content)
    answer = ans_match.group(1).strip() if ans_match else None

    # 提取线索提示词列表：紧跟 "Pinpoint Clues :" 后面 5 词
    clues = []
    clues_match = re.search(r"Pinpoint Clues\s*:\s*(.+\n.+\n.+\n.+\n.+)", content)
    if clues_match:
        lines = clues_match.group(1).splitlines()
        clues = [line.strip() for line in lines if line.strip()]

    return {
        "game_number": game_number,
        "date": date_str,
        "answer": answer,
        "clues": clues
    }


def generate_url_slug(game_number: int, clues: list) -> str:
    """生成 URL slug，格式：linkedin-pinpoint-{game_number}-{clue1}-{clue2}-{clue3}-{clue4}-{clue5}"""
    if not game_number or not clues:
        return ""

    # 将线索词转换为小写并连接
    clue_parts = [clue.lower() for clue in clues[:5]]  # 只取前5个线索
    slug_parts = ["linkedin", "pinpoint", str(game_number)] + clue_parts
    return "-".join(slug_parts)


def insert_to_supabase(data: dict) -> bool:
    """将数据插入到 Supabase 数据库，如果已存在则更新"""
    try:
        # 创建 Supabase 客户端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

        # 准备数据
        clues = data.get("clues", [])
        # 确保有5个线索词，不足的用空字符串填充
        while len(clues) < 5:
            clues.append("")

        url_slug = generate_url_slug(data.get("game_number"), clues)
        game_number = data.get("game_number")

        # 检查是否已存在
        existing = supabase.table("pinpoint_daily_answers").select("id").eq("game_number", game_number).execute()

        insert_data = {
            "game_number": game_number,
            "date": data.get("date"),
            "answer": data.get("answer"),
            "clue_word_1": clues[0] if len(clues) > 0 else "",
            "clue_word_2": clues[1] if len(clues) > 1 else "",
            "clue_word_3": clues[2] if len(clues) > 2 else "",
            "clue_word_4": clues[3] if len(clues) > 3 else "",
            "clue_word_5": clues[4] if len(clues) > 4 else "",
            "url_slug": url_slug,
            "status": "published"
        }

        if existing.data:
            # 记录已存在，执行更新
            result = supabase.table("pinpoint_daily_answers").update(insert_data).eq("game_number", game_number).execute()
            print(f"🔄 游戏编号 {game_number} 已存在，数据已更新")
        else:
            # 记录不存在，执行插入
            result = supabase.table("pinpoint_daily_answers").insert(insert_data).execute()
            print(f"✅ 新数据已成功插入到 Supabase，游戏编号: {game_number}")

        return True

    except Exception as e:
        print(f"❌ 操作 Supabase 失败: {str(e)}")
        return False


def fetch_pinpoint():
    """获取 Pinpoint 数据，带重试机制和更好的错误处理"""
    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 配置请求会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    # 重试配置
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            print(f"🔄 尝试获取数据 (第 {attempt + 1}/{max_retries} 次)...")

            # 尝试不同的请求配置
            if attempt == 0:
                # 第一次尝试：正常请求
                resp = session.get(PINPOINT_URL, timeout=15)
            elif attempt == 1:
                # 第二次尝试：禁用 SSL 验证
                resp = session.get(PINPOINT_URL, timeout=15, verify=False)
            else:
                # 第三次尝试：使用更宽松的 SSL 配置
                resp = session.get(PINPOINT_URL, timeout=20, verify=False,
                                 headers={'Connection': 'close'})

            resp.raise_for_status()
            parsed = parse_pinpoint_page(resp.text)
            parsed["fetched_at"] = datetime.utcnow().isoformat()
            print("✅ 数据获取成功！")
            return parsed

        except requests.exceptions.SSLError as e:
            print(f"❌ SSL 错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                print("❌ 所有重试都失败了，SSL 连接问题无法解决")
                raise

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise

        except Exception as e:
            print(f"❌ 未知错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise


if __name__ == "__main__":
    print("🔍 正在获取 Pinpoint 游戏数据...")
    data = fetch_pinpoint()

    print("📊 获取到的数据:")
    print(json.dumps(data, ensure_ascii=False, indent=2))

    # 检查数据完整性
    if data.get("game_number") and data.get("date") and data.get("answer") and data.get("clues"):
        print("\n💾 正在插入数据到 Supabase...")
        success = insert_to_supabase(data)
        if success:
            print("🎉 任务完成！")
        else:
            print("⚠️ 数据插入失败，请检查错误信息")
    else:
        print("⚠️ 数据不完整，跳过数据库插入")
        missing_fields = []
        if not data.get("game_number"):
            missing_fields.append("game_number")
        if not data.get("date"):
            missing_fields.append("date")
        if not data.get("answer"):
            missing_fields.append("answer")
        if not data.get("clues"):
            missing_fields.append("clues")
        print(f"缺失字段: {', '.join(missing_fields)}")