#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timezone
import json
from supabase import create_client, Client
import urllib3
import ssl
import os
import time

PINPOINT_URL = "https://www.dailycode.com.ng/Pinpoint-hint-answer-today"

# Supabase 配置
SUPABASE_URL = "https://urjvwlqymdokurivceni.supabase.co"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVyanZ3bHF5bWRva3VyaXZjZW5pIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDM5ODk4MywiZXhwIjoyMDY1OTc0OTgzfQ.rs-gne1v4__hymLb2Q9oyC4je0_AxJSnD81shQYCEzM"

def parse_pinpoint_page(html: str) -> dict:
    soup = BeautifulSoup(html, "html.parser")

    # 获取页面标题中的日期和编号
    title = soup.find("title")
    h2 = soup.find("h2")
    date_str = None
    game_number = None

    # 从标题或H2中提取游戏编号和日期 "Today LinkedIn Pinpoint Clues & Answer #460 August 3, 2025"
    title_text = ""
    if title:
        title_text = title.get_text()
    elif h2:
        title_text = h2.get_text()
    
    if title_text:
        # 匹配游戏编号
        number_match = re.search(r"#(\d+)", title_text)
        if number_match:
            game_number = int(number_match.group(1))
        
        # 匹配日期 "August 3, 2025"
        date_match = re.search(r"(\w+\s+\d+,\s+\d{4})", title_text)
        if date_match:
            date_str_raw = date_match.group(1)
            try:
                # 转换为标准格式
                date_obj = datetime.strptime(date_str_raw, "%B %d, %Y")
                date_str = date_obj.strftime("%Y-%m-%d")
            except:
                # 如果解析失败，尝试其他格式
                try:
                    date_obj = datetime.strptime(date_str_raw, "%b %d, %Y")
                    date_str = date_obj.strftime("%Y-%m-%d")
                except:
                    date_str = None

    # 提取答案 - 等待30秒后直接查找"Today Answer is:"后面的内容
    answer = None

    # 查找"Today Answer is:"标题后面的内容
    # 尝试不同的匹配方式
    answer_heading = soup.find("h3", string="Today Answer is:")
    if not answer_heading:
        answer_heading = soup.find("h3", string=lambda text: text and "Today Answer is" in text)
    if not answer_heading:
        answer_heading = soup.find("h3", text=re.compile(r"Today Answer is", re.IGNORECASE))

    if answer_heading:
        # 查找紧跟在标题后面的内容
        next_element = answer_heading.find_next_sibling()
        while next_element:
            # 获取完整的文本内容，包括所有子元素
            text = next_element.get_text(separator=' ', strip=True)

            if text and len(text) > 0 and not text.isdigit():
                # 清理答案文本
                answer = text.strip()

                # 处理编码问题，将特殊字符替换为正确的引号
                answer = answer.replace('â', '"').replace('â', '"')
                answer = answer.replace(''', "'").replace(''', "'")
                answer = answer.replace('â€œ', '"').replace('â€', '"')  # 添加更多编码修复

                # 修复更多编码问题
                answer = answer.replace('\x80\x9c', '"').replace('\x80\x9d', '"')  # 修复损坏的UTF-8引号
                answer = answer.replace('\x80\x98', "'").replace('\x80\x99', "'")  # 修复损坏的UTF-8单引号
                answer = answer.replace('\x80\x93', '-').replace('\x80\x94', '—')  # 修复破折号

                # 尝试修复其他可能的编码问题
                try:
                    # 如果包含无效字符，尝试重新编码
                    answer = answer.encode('utf-8', errors='ignore').decode('utf-8')
                except:
                    pass

                # 修复双重引号问题
                answer = answer.replace('""', '"')  # 将双引号替换为单引号
                answer = answer.replace('\"\"', '"')  # 修复转义的双引号

                # 移除感叹号
                answer = answer.replace('!', '').strip()

                # 如果包含"Today Clues is"，在这里截断
                if "Today Clues is" in answer:
                    answer = answer.split("Today Clues is")[0].strip()

                # 如果包含换行，只取第一行（但保持完整的答案）
                lines = answer.split('\n')
                if len(lines) > 1:
                    # 找到最长的有意义的行作为答案
                    answer = max(lines, key=len).strip()

                # 更智能的答案验证：
                # 1. 长度至少3个字符（允许短答案如 "Tea", "Red"）
                # 2. 包含字母（排除纯符号）
                # 3. 不是常见的无意义文本
                if (len(answer) >= 3 and
                    any(c.isalpha() for c in answer)):
                    break
            next_element = next_element.find_next_sibling()

    # 提取线索词 - 直接查找"Today Clues is:"后面的内容
    clues = []

    # 查找"Today Clues is:"标题后面的内容
    # 尝试不同的匹配方式
    clues_heading = soup.find("h3", string="Today Clues is:")
    if not clues_heading:
        clues_heading = soup.find("h3", string=lambda text: text and "Today Clues is" in text)
    if not clues_heading:
        clues_heading = soup.find("h3", text=re.compile(r"Today Clues is", re.IGNORECASE))

    if clues_heading:
        # 查找紧跟在标题后面的内容
        next_element = clues_heading.find_next_sibling()
        while next_element:
            text = next_element.get_text().strip()
            if text:
                # 分割逗号分隔的线索
                clue_words = [word.strip() for word in text.split(',')]
                clues.extend(clue_words)
                break
            next_element = next_element.find_next_sibling()

    return {
        "game_number": game_number,
        "date": date_str,
        "answer": answer,
        "clues": clues[:5]  # 只取前5个线索
    }


def generate_url_slug(game_number: int, clues: list) -> str:
    """生成 URL slug，格式：linkedin-pinpoint-{game_number}-{clue1}-{clue2}-{clue3}-{clue4}-{clue5}"""
    if not game_number or not clues:
        return ""

    # 将线索词转换为小写并连接
    clue_parts = [clue.lower() for clue in clues[:5]]  # 只取前5个线索
    slug_parts = ["linkedin", "pinpoint", str(game_number)] + clue_parts
    return "-".join(slug_parts)


def insert_to_supabase(data: dict) -> bool:
    """将数据插入到 Supabase 数据库，如果已存在则更新"""
    try:
        # 创建 Supabase 客户端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

        # 准备数据
        clues = data.get("clues", [])
        # 确保有5个线索词，不足的用空字符串填充
        while len(clues) < 5:
            clues.append("")

        url_slug = generate_url_slug(data.get("game_number"), clues)
        game_number = data.get("game_number")

        # 检查是否已存在
        existing = supabase.table("pinpoint_daily_answers").select("id").eq("game_number", game_number).execute()

        insert_data = {
            "game_number": game_number,
            "date": data.get("date"),
            "answer": data.get("answer"),
            "clue_word_1": clues[0] if len(clues) > 0 else "",
            "clue_word_2": clues[1] if len(clues) > 1 else "",
            "clue_word_3": clues[2] if len(clues) > 2 else "",
            "clue_word_4": clues[3] if len(clues) > 3 else "",
            "clue_word_5": clues[4] if len(clues) > 4 else "",
            "url_slug": url_slug,
            "status": "published"
        }

        if existing.data:
            # 记录已存在，执行更新
            result = supabase.table("pinpoint_daily_answers").update(insert_data).eq("game_number", game_number).execute()
            print(f"🔄 游戏编号 {game_number} 已存在，数据已更新")
        else:
            # 记录不存在，执行插入
            result = supabase.table("pinpoint_daily_answers").insert(insert_data).execute()
            print(f"✅ 新数据已成功插入到 Supabase，游戏编号: {game_number}")

        return True

    except Exception as e:
        print(f"❌ 操作 Supabase 失败: {str(e)}")
        return False


def fetch_pinpoint_with_wait():
    """获取 Pinpoint 数据，等待30秒让答案动态加载"""
    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 配置请求会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    # 重试配置
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            print(f"🔄 尝试获取数据 (第 {attempt + 1}/{max_retries} 次)...")

            # 获取页面内容
            resp = session.get(PINPOINT_URL, timeout=15)
            resp.raise_for_status()
            
            # 第一次解析，检查是否已经有答案
            parsed = parse_pinpoint_page(resp.text)
            
            if parsed.get("answer") and len(parsed.get("answer", "")) >= 3:
                # 如果已经获取到完整答案，直接返回
                print("✅ 答案已经可用，无需等待")
                parsed["fetched_at"] = datetime.now(timezone.utc).isoformat()
                return parsed
            else:
                # 如果答案还没有加载，等待30秒
                print("⏳ 答案尚未加载，等待30秒...")
                time.sleep(30)
                
                # 重新获取页面
                print("🔄 重新获取页面内容...")
                resp = session.get(PINPOINT_URL, timeout=15)
                resp.raise_for_status()
                
                # 重新解析
                parsed = parse_pinpoint_page(resp.text)
                parsed["fetched_at"] = datetime.now(timezone.utc).isoformat()
                
                if parsed.get("answer"):
                    print("✅ 等待后成功获取答案！")
                else:
                    print("⚠️ 等待后仍未获取到答案，但继续处理...")
                
                return parsed

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise

        except Exception as e:
            print(f"❌ 未知错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise


if __name__ == "__main__":
    print("🔍 正在获取 DailyCode Pinpoint 游戏数据...")
    print("⏰ 注意：此网站的答案需要等待约30秒才会显示")
    
    data = fetch_pinpoint_with_wait()

    print("📊 获取到的数据:")
    print(json.dumps(data, ensure_ascii=False, indent=2))

    # 检查数据完整性
    if data.get("game_number") and data.get("date") and data.get("clues"):
        print("\n💾 正在插入数据到 Supabase...")
        success = insert_to_supabase(data)
        if success:
            print("🎉 任务完成！")
        else:
            print("⚠️ 数据插入失败，请检查错误信息")
    else:
        print("⚠️ 数据不完整，跳过数据库插入")
        missing_fields = []
        if not data.get("game_number"):
            missing_fields.append("game_number")
        if not data.get("date"):
            missing_fields.append("date")
        if not data.get("answer"):
            missing_fields.append("answer")
        if not data.get("clues"):
            missing_fields.append("clues")
        print(f"缺失字段: {', '.join(missing_fields)}")
