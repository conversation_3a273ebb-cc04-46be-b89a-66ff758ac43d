#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import json

PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/"


def parse_pinpoint_page(html: str) -> dict:
    soup = BeautifulSoup(html, "html.parser")

    # 获取页面标题中的日期和编号（作为备用）
    h2 = soup.find("h2")
    date_str = None
    game_number = None
    if h2 and re.search(r"Today's Pinpoint Answer\s*\(\d{4}-\d{2}-\d{2}\)", h2.get_text()):
        m = re.search(r"\((\d{4}-\d{2}-\d{2})\)", h2.get_text())
        if m:
            date_str = m.group(1)
        body = h2.next_element
        # find pin number under same element
        m2 = re.search(r"Game\s*#(\d+)", html)
        if m2:
            game_number = int(m2.group(1))

    # 文本提取整体块
    content = soup.get_text(separator="\n")
    # 提取答案类别： "Pinpoint Today Answer :" 后面行
    ans_match = re.search(r"Pinpoint Today Answer\s*:\s*\n?(.+)", content)
    answer = ans_match.group(1).strip() if ans_match else None

    # 提取线索提示词列表：紧跟 "Pinpoint Clues :" 后面 5 词
    clues = []
    clues_match = re.search(r"Pinpoint Clues\s*:\s*(.+\n.+\n.+\n.+\n.+)", content)
    if clues_match:
        lines = clues_match.group(1).splitlines()
        clues = [line.strip() for line in lines if line.strip()]

    return {
        "game_number": game_number,
        "date": date_str,
        "answer": answer,
        "clues": clues
    }


def fetch_pinpoint():
    resp = requests.get(PINPOINT_URL, timeout=10)
    resp.raise_for_status()
    parsed = parse_pinpoint_page(resp.text)
    parsed["fetched_at"] = datetime.utcnow().isoformat()
    return parsed


if __name__ == "__main__":
    data = fetch_pinpoint()
    print(json.dumps(data, ensure_ascii=False, indent=2))